#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音效文件重命名脚本
将英文音效文件名翻译成中文
"""

import os
import shutil

def rename_audio_files():
    """重命名音效文件从英文到中文"""
    
    # 英文到中文的翻译映射
    translation_map = {
        "21.wav": "21.wav",  # 保持数字不变
        "BABA.wav": "爸爸.wav",
        "DEFEAT.wav": "失败.wav",
        "SLAM.wav": "重击.wav",
        "YOU.wav": "你.wav",
        "aah.wav": "啊.wav",
        "adofai_fire.wav": "adofai_火焰.wav",
        "adofai_ice.wav": "adofai_冰块.wav",
        "adofaicymbal.wav": "adofai_钹.wav",
        "adofaikick.wav": "adofai_踢击.wav",
        "airhorn.wav": "气笛.wav",
        "alarm.wav": "警报.wav",
        "alert.wav": "提醒.wav",
        "americano.wav": "美式咖啡.wav",
        "augh.wav": "痛苦声.wav",
        "babylaugh.wav": "婴儿笑声.wav",
        "bell.wav": "铃铛.wav",
        "bleep.wav": "哔声.wav",
        "boing.wav": "弹跳声.wav",
        "boink.wav": "撞击声.wav",
        "bong.wav": "钟声.wav",
        "bonk.wav": "敲击声.wav",
        "boom.wav": "爆炸声.wav",
        "boowomp.wav": "低沉音.wav",
        "bruh.wav": "兄弟.wav",
        "builttoscale.wav": "按比例建造.wav",
        "bup.wav": "嘟声.wav",
        "buttonpop.wav": "按钮弹出.wav",
        "buzzer.wav": "蜂鸣器.wav",
        "cave.wav": "洞穴.wav",
        "celeste_dash.wav": "celeste_冲刺.wav",
        "celeste_death.wav": "celeste_死亡.wav",
        "celeste_spring.wav": "celeste_弹簧.wav",
        "choruskid.wav": "合唱小孩.wav",
        "clap.wav": "拍手.wav",
        "cowbell.wav": "牛铃.wav",
        "dimrainsynth.wav": "暗雨合成器.wav",
        "dkremote.wav": "dk遥控器.wav",
        "dodgeball.wav": "躲避球.wav",
        "dog.wav": "狗叫.wav",
        "e.wav": "e.wav",  # 保持字母不变
        "eight.wav": "八.wav",
        "error.wav": "错误.wav",
        "explosion.wav": "爆炸.wav",
        "familyguy.wav": "家庭伙伴.wav",
        "fart.wav": "放屁.wav",
        "fireinthehole.wav": "火在洞里.wav",
        "flipnote.wav": "翻页笔记.wav",
        "fnf_down.wav": "fnf_下.wav",
        "fnf_left.wav": "fnf_左.wav",
        "fnf_up.wav": "fnf_上.wav",
        "gdexplosion.wav": "gd爆炸.wav",
        "geometrydash.wav": "几何冲刺.wav",
        "getout.wav": "出去.wav",
        "gnome.wav": "地精.wav",
        "granddad.wav": "爷爷.wav",
        "gun.wav": "枪声.wav",
        "hammer.wav": "锤子.wav",
        "hehehehaw.wav": "嘿嘿嘿哈.wav",
        "hitmarker.wav": "命中标记.wav",
        "hoenn.wav": "丰缘.wav",
        "honk.wav": "喇叭声.wav",
        "jermawoo.wav": "杰玛呜.wav",
        "kaching.wav": "收钱声.wav",
        "karateman_hit.wav": "空手道_击中.wav",
        "karateman_offbeat.wav": "空手道_失拍.wav",
        "karateman_throw.wav": "空手道_投掷.wav",
        "kick.wav": "踢击.wav",
        "krabs.wav": "蟹老板.wav",
        "mariopaint_baby.wav": "马里奥绘画_婴儿.wav",
        "mariopaint_car.wav": "马里奥绘画_汽车.wav",
        "mariopaint_cat.wav": "马里奥绘画_猫.wav",
        "mariopaint_dog.wav": "马里奥绘画_狗.wav",
        "mariopaint_flower.wav": "马里奥绘画_花.wav",
        "mariopaint_gameboy.wav": "马里奥绘画_游戏机.wav",
        "mariopaint_luigi.wav": "马里奥绘画_路易吉.wav",
        "mariopaint_mario.wav": "马里奥绘画_马里奥.wav",
        "mariopaint_plane.wav": "马里奥绘画_飞机.wav",
        "mariopaint_star.wav": "马里奥绘画_星星.wav",
        "mariopaint_swan.wav": "马里奥绘画_天鹅.wav",
        "metalpipe.wav": "金属管.wav",
        "midspin.wav": "中旋.wav",
        "morningflower.wav": "晨花.wav",
        "morshu.wav": "莫尔舒.wav",
        "mrbeast.wav": "野兽先生.wav",
        "necoarc.wav": "猫弧.wav",
        "nerdhorn.wav": "书呆子号角.wav",
        "noice.wav": "不错.wav",
        "nope.wav": "不.wav",
        "noteblock_bass.wav": "音符块_低音.wav",
        "noteblock_click.wav": "音符块_点击.wav",
        "noteblock_harp.wav": "音符块_竖琴.wav",
        "noteblock_snare.wav": "音符块_军鼓.wav",
        "obama.wav": "奥巴马.wav",
        "oof.wav": "哎呀.wav",
        "ook.wav": "呜.wav",
        "op.wav": "op.wav",  # 保持缩写
        "otto_happy.wav": "奥托_开心.wav",
        "otto_off.wav": "奥托_关闭.wav",
        "otto_on.wav": "奥托_开启.wav",
        "otto_stress.wav": "奥托_压力.wav",
        "pan.wav": "平底锅.wav",
        "perfectfail.wav": "完美失败.wav",
        "pianoriff.wav": "钢琴即兴.wav",
        "pingas.wav": "品加斯.wav",
        "pizza.wav": "披萨.wav",
        "preecho.wav": "预回声.wav",
        "puyo.wav": "魔法气泡.wav",
        "quack.wav": "鸭叫.wav",
        "rdclap.wav": "rd拍手.wav",
        "rdmistake.wav": "rd错误.wav",
        "recordscratch.wav": "唱片刮擦.wav",
        "ride2.wav": "骑行2.wav",
        "robtopphone.wav": "robtop电话.wav",
        "samsung.wav": "三星.wav",
        "samurai.wav": "武士.wav",
        "sans_voice.wav": "sans_声音.wav",
        "shaker.wav": "摇铃.wav",
        "shatter.wav": "破碎.wav",
        "sidestick.wav": "边击.wav",
        "skeleton.wav": "骷髅.wav",
        "skillstar.wav": "技能星.wav",
        "skipshot.wav": "跳跃射击.wav",
        "slap.wav": "拍击.wav",
        "slapbass.wav": "拍击贝斯.wav",
        "slip.wav": "滑倒.wav",
        "sm64_hurt.wav": "sm64_受伤.wav",
        "sm64_painting.wav": "sm64_绘画.wav",
        "smm_scream.wav": "smm_尖叫.wav",
        "smokealarm.wav": "烟雾警报.wav",
        "smw_1up.wav": "smw_1up.wav",
        "smw_coin.wav": "smw_硬币.wav",
        "smw_kick.wav": "smw_踢击.wav",
        "smw_spinjump.wav": "smw_旋转跳跃.wav",
        "smw_stomp.wav": "smw_踩踏.wav",
        "smw_stomp2.wav": "smw_踩踏2.wav",
        "smw_yoshi.wav": "smw_耀西.wav",
        "steve_oof.wav": "史蒂夫_哎呀.wav",
        "stopposting.wav": "停止发帖.wav",
        "subaluwa.wav": "苏巴鲁瓦.wav",
        "suspense.wav": "悬疑.wav",
        "tab_actions.wav": "标签_动作.wav",
        "tab_decorations.wav": "标签_装饰.wav",
        "tab_rooms.wav": "标签_房间.wav",
        "tab_rows.wav": "标签_行.wav",
        "tab_sounds.wav": "标签_声音.wav",
        "tada.wav": "哒哒.wav",
        "taiko_don.wav": "太鼓_咚.wav",
        "taiko_ka.wav": "太鼓_咔.wav",
        "taunt.wav": "嘲讽.wav",
        "tf2_crit.wav": "tf2_暴击.wav",
        "thwomp.wav": "砰砰.wav",
        "tom.wav": "汤姆.wav",
        "tonk.wav": "咚.wav",
        "ultrainstinct.wav": "极意自在功.wav",
        "undertale_crack.wav": "undertale_破裂.wav",
        "undertale_hit.wav": "undertale_击中.wav",
        "wateronthehill.wav": "山上的水.wav",
        "waterphone.wav": "水琴.wav",
        "whatsapp.wav": "WhatsApp.wav",
        "whipcrack.wav": "鞭子声.wav",
        "wowowow.wav": "哇哇哇.wav",
        "yahoo.wav": "雅虎.wav",
        "yawn.wav": "打哈欠.wav",
        "yoda.wav": "尤达.wav",
        "zunpet.wav": "祖宠.wav"
    }
    
    # 获取当前目录
    current_dir = "."
    
    # 统计信息
    renamed_count = 0
    skipped_count = 0
    
    print("开始重命名音效文件...")
    print("=" * 50)
    
    # 遍历翻译映射并重命名文件
    for old_name, new_name in translation_map.items():
        old_path = os.path.join(current_dir, old_name)
        new_path = os.path.join(current_dir, new_name)
        
        # 检查原文件是否存在
        if os.path.exists(old_path):
            # 检查新文件名是否已存在
            if os.path.exists(new_path) and old_path != new_path:
                print(f"跳过: {old_name} -> {new_name} (目标文件已存在)")
                skipped_count += 1
                continue
            
            # 如果新旧文件名相同，跳过
            if old_name == new_name:
                print(f"保持: {old_name} (无需更改)")
                continue
                
            try:
                # 重命名文件
                shutil.move(old_path, new_path)
                print(f"重命名: {old_name} -> {new_name}")
                renamed_count += 1
            except Exception as e:
                print(f"错误: 无法重命名 {old_name} -> {new_name}: {e}")
                skipped_count += 1
        else:
            print(f"文件不存在: {old_name}")
            skipped_count += 1
    
    print("=" * 50)
    print(f"重命名完成!")
    print(f"成功重命名: {renamed_count} 个文件")
    print(f"跳过: {skipped_count} 个文件")

if __name__ == "__main__":
    # 确认操作
    print("此脚本将把音效文件名从英文翻译成中文")
    print("请确认您要继续操作...")
    
    response = input("输入 'y' 或 'yes' 继续，其他任何键取消: ").lower().strip()
    
    if response in ['y', 'yes']:
        rename_audio_files()
    else:
        print("操作已取消")
